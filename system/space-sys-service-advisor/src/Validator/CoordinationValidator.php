<?php

namespace App\Validator;

use Symfony\Component\Validator\Constraints as Assert;

class CoordinationValidator
{
    /**
     * Get Country constraints.
     *
     * @return array
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\Regex(array(
                'pattern' => '/^-?\d+(\.\d+)?/'
                )
            )
        ];
    }
}
