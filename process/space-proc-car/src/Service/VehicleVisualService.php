<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class VehicleVisualService
{
    use LoggerTrait;

    /**
     * @param HttpClientInterface $client
     */
    public function __construct(
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private CacheParametersService $cacheParametersService,
        private CacheAdapter $cache,
        private array $visualSettings
    ) {
    }

    /**
     * get visual vehicl from BO (v3D or default image..)
     * @param string $brand
     * @param ?string $lcdv
     */
    public function getVisualVehicle(string $brand, ?string $lcdv)
    {
        $this->logger->info('Get LVH Label');
        $key = "api_car_visual_{$brand}_{$lcdv}";
        $data = $this->cache->get(
            $key,
            function (ItemInterface $item) use ($lcdv, $brand) {


                $data = $this->mongoAtlasQueryService->aggregate(
                    'vehicleLabel',
                    [
                        [
                            '$match' => [
                                'lcdv' => [
                                    '$in' => LcdvsProvider::getLcdvsFormatArray($lcdv)
                                ]
                            ]
                        ],
                        //add a field named lcdvLenght dinamically calculated that contains the length of the lcdv
                        [
                            '$addFields' => [
                                'lcdvLength' => [
                                    '$cond' => [
                                        'if' => ['$isArray' => '$lcdv'],
                                        'then' => ['$max' => ['$map' => [
                                            'input' => '$lcdv',
                                            'as' => 'lcdvItem',
                                            'in' => ['$strLenCP' => '$$lcdvItem']
                                        ]]],
                                        'else' => ['$strLenCP' => '$lcdv']
                                    ]
                                ]
                            ]
                        ],
                        // apply a descending sort on the lcdvLength field
                        [
                            '$sort' => [
                                'lcdvLength' => -1
                            ]
                        ],
                        // remove the lcdvLength field from the final result
                        [
                            '$project' => [
                                'lcdvLength' => 0
                            ]
                        ],
                        // limit the result to 1 document
                        [
                            '$limit' => 1
                        ]
                    ]
                );   

                $cacheExpired = $this->cacheParametersService->getCacheExpireDuration();
                $item->expiresAfter($cacheExpired);
                $response = $data->getArrayFormat()['success']['documents'][0] ?? [];
                if(isset($response['visualSettings']))
                {
                    $response['visualSettings']['visual'] = $this->visualSettings[$brand]['baseUrl'];
                    if(!isset($response['visualSettings']['width']) || empty($response['visualSettings']['width'])){
                        $response['visualSettings']['width'] = $this->visualSettings['width'];
                    }
                    
                    if(!isset($response['visualSettings']['height']) || empty($response['visualSettings']['height'])){
                        $response['visualSettings']['height'] = $this->visualSettings['height'];
                    }
                }
                return $response ?? [];
                
            }
        );
        
        return (new SuccessResponse())->setData($data);
    }

}
