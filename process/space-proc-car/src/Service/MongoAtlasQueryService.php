<?php

namespace App\Service;

use App\Exception\MongoAtlasUrlNotProvidedException;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Model\MongoQueryModel;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

/**
 * This service helps to interact with mongoAtlas api.
 */
class MongoAtlasQueryService
{
    use LoggerTrait;

    private $query;

    public function __construct(private NormalizerInterface $normalizer, private MongoAtlasApiConnector $connector, private string $dataSource, string $database)
    {
        $this->query = self::buildQuery($dataSource, $database);
    }

    /**
     * Build mongo query.
     */
    public static function buildQuery(string $dataSource, string $database): MongoQueryModel
    {
        return (new MongoQueryModel())
            ->setDataSource($dataSource)
            ->setDatabase($database);
    }

    /**
     * Perform find request throughout MongoAtlas Api.
     *
     * @param ?array $filter
     */
    public function find(string $collection, ?array $filter, ?int $limit = 1): ResponseArrayFormat
    {
        $this->query->setCollection($collection)
            ->setFilter($filter)
            ->setLimit($limit);

        return $this->execute('find');
    }

    /**
     * Perform aggregate request throughout MongoAtlas Api.
     */
    public function aggregate(string $collection, ?array $pipeline): ResponseArrayFormat
    {
        $this->query->setCollection($collection)
                    ->setPipeline($pipeline ?? []);

        return $this->execute('aggregate');
    }    

    /**
     * Execute Mongo request.
     */
    private function execute(string $action): ResponseArrayFormat
    {
        try {
            $this->logger->info('MongoAtlasQueryService::execute : ' . $action);
            $endpoint = $this->connector->getEndpoint($action);
            $collection = $this->normalizer->normalize($this->query, 'array', ['groups' => ['default', $action]]);
            $response = $this->connector->call(Request::METHOD_POST, $endpoint, ['json' => $collection]);
            return (new SuccessResponse())
                ->setCode($response->getStatusCode())
                ->setData($response->toArray(true));
        } catch (\Exception $e) {
            $this->logger->error('catched Exception in MongoAtlasQueryService::execute :' . $e->getMessage() . json_encode(['json' => $collection]));
            throw MongoAtlasUrlNotProvidedException::make();
        }
    }
}
