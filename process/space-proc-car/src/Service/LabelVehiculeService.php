<?php

declare(strict_types=1);

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Symfony\Contracts\Cache\ItemInterface;

class LabelVehiculeService
{
    use LoggerTrait;

    /**
     * LabelVehiculeService constructor.
     *
     * @param CacheParametersService $cacheParametersService
     * @param CacheAdapter $cache
     */

    public function __construct(
        private CacheParametersService $cacheParametersService,
        private CacheAdapter $cache,
        private MongoAtlasQueryService $mongoAtlasQueryService
    ) {
    }

    /**
     * Get LVH based on the given parameters.
     */
    public function getLvh(
        string $brand,
        string $country,
        string $language,
        string $lcdv
    ): ResponseArrayFormat {
        $this->logger->info('Get LVH Label');
        $culture = strtolower($language) . '-' . strtoupper($country);
        $key = "api_car_lvh_{$brand}_{$lcdv}_{$culture}";
        $data = $this->cache->get(
            $key,
            function (ItemInterface $item) use (
                $lcdv
            ) {

                $data = $this->mongoAtlasQueryService->aggregate(
                    'vehicleLabel',
                    [
                        [
                            '$match' => [
                                'lcdv' => [
                                    '$in' => LcdvsProvider::getLcdvsFormatArray($lcdv)
                                ]
                            ]
                        ],
                        //add a field named lcdvLenght dinamically calculated that contains the length of the lcdv
                        [
                            '$addFields' => [
                                'lcdvLength' => [
                                    '$cond' => [
                                        'if' => ['$isArray' => '$lcdv'],
                                        'then' => ['$max' => ['$map' => [
                                            'input' => '$lcdv',
                                            'as' => 'lcdvItem',
                                            'in' => ['$strLenCP' => '$$lcdvItem']
                                        ]]],
                                        'else' => ['$strLenCP' => '$lcdv']
                                    ]
                                ]
                            ]
                        ],
                        // apply a descending sort on the lcdvLength field
                        [
                            '$sort' => [
                                'lcdvLength' => -1
                            ]
                        ],
                        // remove the lcdvLength field from the final result
                        [
                            '$project' => [
                                'lcdvLength' => 0
                            ]
                        ],
                        // limit the result to 1 document
                        [
                            '$limit' => 1
                        ]
                    ]
                );   

                $cacheExpired = $this->cacheParametersService->getCacheExpireDuration();
                $item->expiresAfter($cacheExpired);
                return $data->getArrayFormat()['success']['documents'][0] ?? null;
            }
        );
        return (new SuccessResponse())->setData($data);
    }
}
