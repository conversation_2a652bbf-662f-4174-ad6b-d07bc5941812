<?php

namespace App\Service;

use App\Exception\CorvetException;
use App\Exception\VehicleNotFoundException;
use App\Trait\LoggerTrait;
use App\Helper\VehicleTypeEntities;
use Exception;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Cache\ItemInterface;

class CorvetService
{
    use LoggerTrait;

    /**
     * @param CorvetSysConnector $corvetSysConnector
     * @param CacheParametersService $cacheParametersService
     * @param CacheAdapter $cache
     */
    public function __construct(
        private CorvetSysConnector $corvetSysConnector,
        private CacheParametersService $cacheParametersService,
        private CacheAdapter $cache
    ) {
    }

    private function getCorvetResponse($brand, $country, $vin, $source = "APP")
    {
        $cacheKey = "api_car_corvet_{$brand}_{$country}_{$vin}_{$source}";
        /**
         * May do duplicated json_encode, but well to optimize redis IO
         */
        $contentResponse = $this->cache->get(
            $cacheKey,
            function (ItemInterface $item) use ($brand, $country, $source, $vin) {
                $item->expiresAfter(10);
                $uri = "/corvet/{$vin}/data";
                $response = $this->corvetSysConnector->call(Request::METHOD_GET, $uri, compact('brand', 'country', 'source'), compact('brand'));

                /**
                 * To not explode redis db, the content string will be stored instead of array (50% of difference)
                 */
                
                if ($response->getStatusCode() != Response::HTTP_OK) {
                    throw CorvetException::make();
                }
                $responseContent = $response->getContent(false);
                $responseArray = $response->toArray(false);
                $corvetResponse = $responseArray['success'] ?? [];
                if (
                    $this->checkVehicleExists($corvetResponse) && isset($corvetResponse['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'])
                ) {
                    $item->expiresAfter($this->cacheParametersService->getCacheExpireDuration());
                } else {
                    $item->expiresAfter(30);
                    throw VehicleNotFoundException::make();
                }
                return $responseContent;
            }
        );
        $response = json_decode($contentResponse, true);
        return $response['success'] ?? [];
    }

    /**
     * @param $brand
     * @param $country
     * @return array
     */
    public function getLcdv($brand, $country, string $vin, string $source = "APP", bool $withAttributes = false): array
    {
        try {
            $this->logger->info('Get vehicle Data for vin ' . $vin);
            $response = $this->getCorvetResponse($brand, $country, $vin, $source);
            $allAttributes = $response['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            if (!is_array($allAttributes)) {
                $allAttributes = [$allAttributes];
            }
            $attributesData = $this->getDataFromAttributes($allAttributes);
            $vehicleData = $response['VEHICULE']['DONNEES_VEHICULE'];
            $data =  [
                "success" => true,
                "code" => Response::HTTP_OK,
                "data" => $vehicleData['LCDV_BASE'],
                "warranty_start_date" => $vehicleData['DATE_DEBUT_GARANTIE'] ? $vehicleData['DATE_DEBUT_GARANTIE'] : '',
                'options' => $attributesData['vehicle_options'] ?? [],
                'attributes' => $attributesData['managed_attributes'] ?? [],
                'type_vehicle' => VehicleTypeEntities::getType('DXD', $attributesData['managed_attributes'] ?? []),
                'types' => VehicleTypeEntities::getTypes($attributesData['managed_attributes'] ?? []),
            ];
            
            return $data;
        } catch (Exception $e) {
            $this->logger->error('Error: Get vehicle Data for vin ' . $vin . '(' . $e->getMessage() . ')');
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    /**
     * @param array $response
     * @return bool
     */
    private function checkVehicleExists(array $response)
    {
        return ($response['VEHICULE']['@attributes']['Existe'] ?? '') == 'O';
    }

    private function getDataFromAttributes(array $attributes)
    {
        $managedAttributes = [];
        $response  = ['vehicle_options' => []];
        foreach ($attributes as $attribute) {
            if (preg_match('/^P(.{4})|^D(.{4})CP$/i', $attribute)) {
                $response['vehicle_options'][] = substr($attribute, 1, 4);
            }

            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                default:
                    break;
            }
        }
        $response['managed_attributes'] = $managedAttributes;

        return $response;
    }
}
