<?php

namespace App\Dto;

use App\Model\FeatureCode;

/**
 * AddVehicleOutputDTO
 */
class AddVehicleOutputDTO
{
    private ?string $addStatus = null;
    private ?string $sdp = null;
    private ?string $year = null;
    private ?string $warrantyStartDate = null;
    private ?int $regTimestamp = null;
    private ?string $type = null;
    private ?string $label = null;
    private ?string $lcdv = null;
    private ?string $visual = null;
    private ?bool $isOrder = null;
    private ?array $featuresCode = null;
    private ?string $nickName = null;
    private ?int $lastUpdate = null;
    private ?string $make = null;
    private ?string $market = null;
    private ?string $culture = null;
    private ?string $connectorType = null;
    private ?string $enrollmentStatus = null;
    private ?string $associationLevel = null;
    private ?bool $isO2X = null;
    private ?string $subMake = null;
    private ?int $featureCodeExpiry = null;
    
    

    /**
     * Get the value of addStatus
     */
    public function getAddStatus(): ?string
    {
        return $this->addStatus;
    }

    /**
     * Set the value of addStatus
     *
     * @return  self
     */
    public function setAddStatus(?string $addStatus): self
    {
        $this->addStatus = $addStatus;

        return $this;
    }

    /**
     * Get the value of year
     */ 
    public function getYear(): ?string
    {
        return $this->year;
    }

    /**
     * Set the value of year
     *
     * @return  self
     */ 
    public function setYear(?string $year):self
    {
        $this->year = $year;

        return $this;
    }

    /**
     * Get the value of warrantyStartDate
     */ 
    public function getWarrantyStartDate(): ?string
    {
        return $this->warrantyStartDate;
    }

    /**
     * Set the value of warrantyStartDate
     *
     * @return  self
     */ 
    public function setWarrantyStartDate(?string $warrantyStartDate): self
    {
        $this->warrantyStartDate = $warrantyStartDate;

        return $this;
    }

    /**
     * Get the value of regTimestamp
     */ 
    public function getRegTimestamp(): ?int
    {
        return $this->regTimestamp;
    }

    /**
     * Set the value of regTimestamp
     *
     * @return  self
     */ 
    public function setRegTimestamp(?int $regTimestamp): self
    {
        $this->regTimestamp = $regTimestamp;

        return $this;
    }

    /**
     * Get the value of type
     */ 
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * Set the value of type
     *
     * @return  self
     */ 
    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get the value of label
     */ 
    public function getLabel(): ?string
    {
        return $this->label;
    }

    /**
     * Set the value of label
     *
     * @return  self
     */ 
    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    /**
     * Get the value of lcdv
     */ 
    public function getLcdv(): ?string
    {
        return $this->lcdv;
    }

    /**
     * Set the value of lcdv
     *
     * @return  self
     */ 
    public function setLcdv(?string $lcdv): self
    {
        $this->lcdv = $lcdv;

        return $this;
    }

    /**
     * Get the value of visual
     */ 
    public function getVisual(): ?string
    {
        return $this->visual;
    }

    /**
     * Set the value of visual
     *
     * @return  self
     */ 
    public function setVisual(?string $visual): self
    {
        $this->visual = $visual;

        return $this;
    }

    /**
     * Get the value of isOrder
     */ 
    public function getIsOrder(): ?bool
    {
        return $this->isOrder;
    }

    /**
     * Set the value of isOrder
     *
     * @return  self
     */ 
    public function setIsOrder(?bool $isOrder): self
    {
        $this->isOrder = $isOrder;

        return $this;
    }

    /**
     * Get the value of sdp
     */ 
    public function getSdp(): ?string
    {
        return $this->sdp;
    }

    /**
     * Set the value of sdp
     *
     * @return  self
     */ 
    public function setSdp(?string $sdp): self
    {
        $this->sdp = $sdp;

        return $this;
    }

    /**
     * Get the value of featuresCode
     */ 
    public function getFeaturesCode(): ?array
    {
        return $this->featuresCode;
    }

    /**
     * Set the value of featuresCode
     *
     * @return  self
     */ 
    public function setFeaturesCode(?array $featuresCode): self
    {
        $this->featuresCode = $featuresCode;

        return $this;
    }

    /**
     * Set the value of featuresCode
     *
     * @return  void
     */ 
    public function addFeaturesCode(FeatureCode $featuresCode)
    {
        $this->featuresCode[] = $featuresCode;
    }

    /**
     * Get the value of nickName
     */ 
    public function getNickName(): ?string
    {
        return $this->nickName;
    }

    /**
     * Set the value of nickName
     *
     * @return  self
     */ 
    public function setNickName(?string $nickName): self
    {
        $this->nickName = $nickName;

        return $this;
    }

    /**
     * Get the value of lastUpdate
     */ 
    public function getLastUpdate(): ?int
    {
        return $this->lastUpdate;
    }

    /**
     * Set the value of lastUpdate
     */ 
    public function setLastUpdate(?int $lastUpdate): self
    {
        $this->lastUpdate = $lastUpdate;

        return $this;
    }


    /**
     * Get the value of make
     */ 
    public function getMake(): ?string
    {
        return $this->make;
    }

    /**
     * Set the value of make
     *
     * @return  self
     */ 
    public function setMake(?string $make): self
    {
        $this->make = $make;

        return $this;
    }

    /**
     * Get the value of market
     */ 
    public function getMarket(): ?string
    {
        return $this->market;
    }

    /**
     * Set the value of market
     *
     * @return  self
     */ 
    public function setMarket(?string $market): self
    {
        $this->market = $market;

        return $this;
    }

    /**
     * Get the value of culture
     */ 
    public function getCulture(): ?string
    {
        return $this->culture;
    }

    /**
     * Set the value of culture
     */ 
    public function setCulture(?string $culture): self
    {
        $this->culture = $culture;

        return $this;
    }

    /**
     * Get the value of connectorType
     */
    public function getConnectorType(): ?string
    {
        return $this->connectorType;
    }

    /**
     * Set the value of connectorType
     */
    public function setConnectorType(?string $connectorType): self
    {
        $this->connectorType = $connectorType;

        return $this;
    }

    /**
     * Get the value of enrollmentStatus
     */
    public function getEnrollmentStatus(): ?string
    {
        return $this->enrollmentStatus;
    }

    /**
     * Set the value of enrollmentStatus
     */
    public function setEnrollmentStatus(?string $enrollmentStatus): self
    {
        $this->enrollmentStatus = $enrollmentStatus;

        return $this;
    }

    /**
     * Get the value of associationLevel
     */
    public function getAssocialtionLevel(): ?string
    {
        return $this->associationLevel;
    }

    /**
     * Set the value of associationLevel
     */
    public function setAssocialtionLevel(?string $associationLevel): self
    {
        $this->associationLevel = $associationLevel;

        return $this;
    }

    /**
     * Get the value of isO2X
     */
    public function getIsO2X(): ?bool
    {
        return $this->isO2X;
    }

    /**
     * Set the value of isO2X
     */
    public function setIsO2X(?bool $isO2X): self
    {
        $this->isO2X = $isO2X;

        return $this;
    }

    /**
     * Get the value of subMake
     */
    public function getSubMake(): ?string
    {
        return $this->subMake;
    }

    /**
     * Set the value of subMake
     */
    public function setSubMake(?string $subMake): self
    {
        $this->subMake = $subMake;

        return $this;
    }

    /**
     * Get the value of featureCodeExpiry
     */
    public function getFeatureCodeExpiry(): ?int
    {
        return $this->featureCodeExpiry;
    }

    /**
     * Set the value of featureCodeExpiry
     */
    public function setFeatureCodeExpiry(?int $featureCodeExpiry): self
    {
        $this->featureCodeExpiry = $featureCodeExpiry;

        return $this;
    }

    /**
     * Set the featureCodeExpiry to 24 hours from now
     */
    public function setFeatureCodeExpiryTo24HoursFromNow(): self
    {
        $this->featureCodeExpiry = time() + (24 * 60 * 60);

        return $this;
    }
}