<?php
namespace App\Controller;

use App\Dto\AddVehicleInputDTO;
use App\Dto\EditVehicleInputDTO;
use App\Helper\ErrorResponse;
use App\Helper\SuccessResponse;
use App\Manager\VehicleManager;
use App\Model\EditVehicleModelSuccess;
use App\Trait\LoggerTrait;
use App\Trait\ValidationResponseTrait;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Validator\VinValidator;
use Nelmio\ApiDocBundle\Attribute\Model;
use Nelmio\ApiDocBundle\Attribute\Security;
use Symfony\Component\HttpFoundation\{Request, Response, JsonResponse};
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Webmozart\Assert\Assert as WebMozartAssert;

#[Route('v2/vehicles', name: 'user_vehicles_v2_')]
class VehicleV2Controller extends AbstractController
{
    use LoggerTrait;
    use ValidationResponseTrait;

    private const BRANDS = ['AC', 'AP', 'DS', 'OP', 'VX', 'SP', 'FT', 'FO', 'AH', 'AR', 'CY', 'DG', 'JE', 'LA', 'RM', 'MA', 'OV'];

    public function __construct(private ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    #[Route(null, name: 'get_user_vehicles', methods: ['GET'])]
    #[OA\Get(
        path: '/v2/vehicles',
        summary: 'Get user\'s vehicles',
        tags: ['Vehicles'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'brand',
                in: 'query',
                description: 'Brand',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'language',
                in: 'query',
                description: 'Language',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'country',
                in: 'query',
                description: 'Country',
                required: true,
                schema: new OA\Schema(type: 'string')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful response',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(
                        properties: [
                            new OA\Property(property: 'vin', type: 'string'),
                            new OA\Property(property: 'lcdv', type: 'string', nullable: true),
                            new OA\Property(property: 'visual', type: 'string', nullable: true),
                            new OA\Property(property: 'short_label', type: 'string', nullable: true),
                            new OA\Property(property: 'nickname', type: 'string', nullable: true),
                            new OA\Property(property: 'warranty_start_date', type: 'integer', nullable: true),
                            new OA\Property(property: 'command', type: 'string', nullable: true),
                            new OA\Property(property: 'sdp', type: 'string')
                        ]
                    )
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Bad Request',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Bad Request::Validation Error: Required Fields Missing',
                content: new JsonContent(type: 'object', properties: [
                    new OA\Property(property: 'error', properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                        new OA\Property(property: 'errors', type: 'object', properties: [
                            new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                        ])
                    ])
                ])
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            )
        ]
    )]
    public function getUserVehicles(Request $request, VehicleManager $vehicleManager): Response
    {
        
        $input = [
            'userId' => $request->headers->get('userId'),
            'brand' => trim(strtoupper($request->query->get('brand'))),
            'language' => trim($request->query->get('language')),
            'country' => trim(strtoupper($request->query->get('country')))
        ];
        $errors = $this->validateInput($input);

        if (!empty($errors)) {
            return $this->json(['error' => $errors], Response::HTTP_BAD_REQUEST);
        }
        $result = $vehicleManager->getUserVehiclesData($input['userId'], $input['brand'], $input['language'], $input['country']);
        if ($result instanceof ErrorResponse) {
            $this->logger->error(
                __METHOD__ . 'An error occurred while fetching user vehicles',
                [
                    $result->getCode(),
                    $result->getErrors()
                ]
            );

            return $this->json(['error' => ['message' => $result->getMessage()]], Response::HTTP_BAD_REQUEST);
        }

        WebMozartAssert::isInstanceOf($result, SuccessResponse::class);
        $data = $result->toArray(false)['content'];
        $data = $vehicleManager->removeNullValues($data);
        return $this->json($data, $result->getCode());
    }

    #[Route('/add', name: 'add_vehicle', methods: ['POST'])]
    #[OA\Post(
        path: '/v2/vehicles/add',
        summary: 'add new vehicle',
        tags: ['Vehicles'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'brand',
                in: 'query',
                description: 'Brand',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'country',
                in: 'query',
                description: 'Country',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'language',
                in: 'query',
                description: 'Language',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'source',
                in: 'query',
                description: 'Source',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\RequestBody(
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'vin',
                            description: 'VIN code',
                            type: 'string',
                            example: 'VR3UPHNKSKT101600'
                        ),
                        new OA\Property(
                            property: 'mileage',
                            description: 'Mileage value for the vehicle; it is a string, but must be rapresent a positive integer; zero also is a valid value',
                            type: 'string',
                            example: '0'
                        ),
                    ]
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 201,
                description: 'Successful response',
                content: new OA\JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'success',
                            description: 'Successfully added'
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: 404,
                description: 'Bad Request',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Bad Request::Validation Error: Required Fields Missing',
                content: new JsonContent(type: 'object', properties: [
                    new OA\Property(property: 'error', properties: [
                        new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                        new OA\Property(property: 'errors', type: 'object', properties: [
                            new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                        ])
                    ])
                ])
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', properties: []),
                        ]),
                    ]
                )
            )
        ]
    )]
    public function addVehicle(Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        $content = json_decode($request->getContent(), true);
        $vin = $content['vin'] ?? '';
        $mileage = $content['mileage'] ?? '0';
        $userId = $request->headers->get('userId', '');
        $brand = $request->query->get('brand', '');
        $language = $request->query->get('language', '');
        $country = $request->query->get('country', '');
        $source = $request->query->get('source', '');

        $errors = $this->validator->validate(
            compact('vin', 'brand', 'country', 'language', 'userId'),
            new Assert\Collection([
                'vin' => VinValidator::getConstraints(),
                'brand' => BrandValidator::getConstraintsForAll(),
                'country' => CountryValidator::getConstraintsForCountry(),
                'language' => LanguageValidator::getConstraintsForLanguage(),
                'userId' => new Assert\NotBlank,
            ])
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }

        $vehiculeInputs = (new AddVehicleInputDTO())
            ->setVin($vin)
            ->setUserId($userId)
            ->setMileage($mileage)
            ->setBrand($brand)
            ->setCountry($country)
            ->setLanguage($language)
            ->setSource($source);

        $response = $vehicleManager->addSSDPVehicle($vehiculeInputs)->toArray(false);
        return $this->json($response['content'], $response['code']);
    }

    #[Route('/info', name: 'info_vehicle', methods: ['GET'])]
    #[OA\Get(
        path: '/v2/vehicles/info',
        summary: 'get vehicle info',
        tags: ['Vehicles'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'vin',
                in: 'header',
                description: 'vin',
                required: false,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'id',
                in: 'query',
                description: 'vehicle id',
                required: false,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'country',
                in: 'query',
                description: 'Country',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'language',
                in: 'query',
                description: 'Language',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'source',
                in: 'query',
                description: 'Source',
                required: true,
                schema: new OA\Schema(type: 'string')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful response',
                content: new OA\JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'success',
                            properties: [
                                new OA\Property(
                                    property: 'vehicleInfo',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'vin', type: 'string'),
                                        new OA\Property(property: 'lcdv', type: 'string'),
                                        new OA\Property(property: 'visual', type: 'string'),
                                        new OA\Property(property: 'short_label', type: 'string', nullable: true),
                                        new OA\Property(property: 'nickname', type: 'string', nullable: true),
                                        new OA\Property(property: 'warranty_start_date', type: 'integer'),
                                        new OA\Property(
                                            property: 'attributes',
                                            type: 'array',
                                            items: new OA\Items(type: 'string')
                                        ),
                                        new OA\Property(property: 'type_vehicle', type: 'integer'),
                                        new OA\Property(
                                            property: 'mileage',
                                            type: 'object',
                                            properties: [
                                                new OA\Property(property: 'value', type: 'integer')
                                            ]
                                        )
                                    ]
                                ),
                                new OA\Property(
                                    property: 'eligibility',
                                    type: 'array',
                                    items: new OA\Items(type: 'string')
                                ),
                                new OA\Property(
                                    property: 'vehicleProducts',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'productsCatalog',
                                            type: 'array',
                                            items: new OA\Items(type: 'string')
                                        ),
                                        new OA\Property(
                                            property: 'purchasedProducts',
                                            type: 'array',
                                            items: new OA\Items(type: 'string')
                                        ),
                                        new OA\Property(
                                            property: 'productGroupNameStatus',
                                            type: 'object'
                                        )
                                    ]
                                ),
                                new OA\Property(property: 'settingsUpdate', type: 'integer')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request'
            )
        ]
    )]
    public function getVehicleInfo(Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        $userId = $request->headers->get('userId', '');
        $vin = $request->headers->get('vin', '');
        $id = $request->query->get('id', null);
        $language = $request->query->get('language', '');
        $country = $request->query->get('country', '');
        $source = $request->query->get('source', '');
        $criteriaValue = $id ?? $vin;
        $critariaKey = $id ? 'id' : 'vin';
        $errors = $this->validator->validate(
            compact('criteriaValue', 'userId', 'country', 'language', 'source'),
            new Assert\Collection([
                'criteriaValue' => new Assert\NotBlank,
                'userId' => new Assert\NotBlank,
                'country' => CountryValidator::getConstraintsForCountry(),
                'language' => LanguageValidator::getConstraintsForLanguage(),
                'source' => new Assert\Choice(['choices' => ['APP','SPACEWEB']])
            ])
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }

        $response = $vehicleManager->getVehicleDetail($userId, $criteriaValue, $language, $country, $critariaKey, $source);
        if ($response instanceof ErrorResponse) {
            return $this->json(['error' => ['message' => $response->getMessage()]], status: $response->getCode());
        }

        return new JsonResponse($response->toArray()['content'], $response->getCode());
    }

    private function validateInput(array $input): array
    {
        $constraints = new Assert\Collection([
            'userId' => [
                new Assert\NotBlank(),
                new Assert\Type('string')
            ],
            'brand' => [
                new Assert\NotBlank(),
                new Assert\Type('string'),
                new Assert\Choice(['choices' => self::BRANDS])
            ],
            'language' => [
                new Assert\NotBlank(),
                new Assert\Type('string'),
                new Assert\Language(),
            ],
            'country' => [
                new Assert\NotBlank(),
                new Assert\Type('string'),
                new Assert\Country(),
            ]
        ]);

        $violations = $this->validator->validate($input, $constraints);

        $errors = [];
        if (count($violations) > 0) {
            foreach ($violations as $violation) {
                $errors[] = $violation->getMessage();
            }
        }

        return $errors;
    }

    #[Route('/edit', name: 'edit_vehicle', methods: ['PUT'])]
    #[OA\Put(
        path: '/v2/vehicles/edit',
        summary: 'Edit vehicle information',
        tags: ['Vehicles'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                example: '120000000k0b4dde874859657ao00001',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'vin',
                in: 'header',
                description: 'Vin',
                required: true,
                example: 'X0234567991034560',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'brand',
                in: 'query',
                description: 'Brand',
                required: true,
                example: 'JE',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'country',
                in: 'query',
                description: 'Country',
                required: true,
                example: 'IT',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'language',
                in: 'query',
                description: 'Language',
                required: true,
                example: 'en',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'source',
                in: 'query',
                description: 'Source',
                required: false,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\RequestBody(
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'nickName',
                            description: 'Nickname of vehicle;it is a string',
                            type: 'string',
                            example: 'MypersonalNickName'
                        ),
                        new OA\Property(
                            property: 'licencePlate',
                            description: 'Licence plate of vehicle;it is a string',
                            type: 'string',
                            example: 'myPlate'
                        ),
                        new OA\Property(
                            property: 'mileage',
                            description: 'Mileage value for the vehicle; it must be represent a positive integer; zero also is a valid value',
                            type: 'integer',
                            example: 0
                        ),
                    ]
                )
            )
        ],
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'Successful response',
                content: new Model(type: EditVehicleModelSuccess::class)
            ),
            new OA\Response(
                response: Response::HTTP_NOT_FOUND,
                description: 'Invalid token',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: Response::HTTP_UNPROCESSABLE_ENTITY,
                description: 'Validation error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', type: 'object'),
                        ]),
                    ]
                )
            )
        ]
    )]
    public function editVehicle(Request $request, VehicleManager $vehicleManager): Response
    {
        $content = $request->toArray();
        $vin = $request->headers->get('vin');
        $mileage = $content['mileage'] ?? null;
        $nickName = $content['nickName'] ?? '';
        $licencePlate = $content['licencePlate'] ?? '';
        $userId = $request->headers->get('userId');
        $brand = $request->query->get('brand');
        $language = $request->query->get('language');
        $country = $request->query->get('country');
        $source = $request->query->get('source');

        $errors = $this->validator->validate(
            compact('vin', 'brand', 'country', 'language', 'userId'),
            new Assert\Collection([
                'vin' => VinValidator::getConstraints(),
                'brand' => BrandValidator::getConstraintsForAll(),
                'country' => CountryValidator::getConstraintsForCountry(),
                'language' => LanguageValidator::getConstraintsForLanguage(),
                'userId' => new Assert\NotBlank,
            ])
        );

        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }

        $vehicleInputs = (new EditVehicleInputDTO())
            ->setVin($vin)
            ->setUserId($userId)
            ->setMileage($mileage)
            ->setBrand($brand)
            ->setCountry($country)
            ->setLanguage($language)
            ->setNickName($nickName)
            ->setLicencePlate($licencePlate)
            ->setSource($source);

        $response = $vehicleManager->editVehicle($vehicleInputs);

        return $this->json(
            $response->toArray(false)['content'],
            $response->getCode()
        );
    }

    #[Route('/delete', name: 'deleteVehiclefromUserGarage', methods: ['DELETE'])]
    #[OA\Delete(
        path: '/v2/vehicles/delete',
        summary: 'Delete vehicle information',
        tags: ['Vehicles'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                example: '120000000k0b4dde874859657ao00001',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'vin',
                in: 'header',
                description: 'Vin',
                required: true,
                example: 'X0234567991034560',
                schema: new OA\Schema(type: 'string')
            ),
            // new OA\Parameter(
            //     name: 'brand',
            //     in: 'query',
            //     description: 'Brand',
            //     required: false,
            //     example: 'JE',
            //     schema: new OA\Schema(type: 'string')
            // ),
            // new OA\Parameter(
            //     name: 'country',
            //     in: 'query',
            //     description: 'Country',
            //     required: false,
            //     example: 'IT',
            //     schema: new OA\Schema(type: 'string')
            // )
        ],
        responses: [
            new OA\Response(
                response: Response::HTTP_NO_CONTENT,
                description: 'Successful response with no content',
            ),
            new OA\Response(
                response: Response::HTTP_NOT_FOUND,
                description: 'Invalid token',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                        ]),
                    ]
                )
            ),
            new OA\Response(
                response: Response::HTTP_UNPROCESSABLE_ENTITY,
                description: 'Validation error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message'),
                            new OA\Property(property: 'errors', type: 'object'),
                        ]),
                    ]
                )
            )
        ]
    )]
    public function deleteVehicle(Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        try {
            $vin = $request->headers->get('vin');
            $userId = $request->headers->get('userId');
            // $country = $request->query->get('country');
            // $source = $request->query->get('source');
            // $brand = $request->query->get('brand');

            // $errors = $this->validator->validate(
            //     compact('vin', 'brand', 'country', 'userId'),
            //     new Assert\Collection([
            //         'vin' => VinValidator::getConstraints(),
            //         'brand' => BrandValidator::getConstraintsForAll(),
            //         'country' => CountryValidator::getConstraintsForCountry(),
            //         'userId' => new Assert\NotBlank,
            //     ])
            // );
            $errors = $this->validator->validate(
                compact('vin', 'userId'),
                new Assert\Collection([
                    'vin' => VinValidator::getConstraints(),
                    'userId' => new Assert\NotBlank,
                ])
            );

            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }
            
            $response = $vehicleManager->deleteVehicle($userId, $vin);

            return $this->json(
                $response->toArray(false)['content'],
                $response->getCode()
            );
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/features', name: 'vehicle_features', methods: ['GET'])]
    #[OA\Tag(name: 'Features')]
    #[OA\Get(
        path: '/v2/vehicles/features',
        summary: 'Get vehicle features',
        tags: ['Feature'],
        parameters: [
            new OA\Parameter(
                name: 'userId',
                in: 'header',
                description: 'User ID',
                required: true,
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'vin',
                in: 'header',
                description: 'Vehicle Identification Number',
                required: true,
                schema: new OA\Schema(type: 'string')
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful response',
                content: new OA\JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'success',
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'features',
                                    type: 'array',
                                    items: new OA\Items(
                                        type: 'object',
                                        properties: [
                                            new OA\Property(property: 'code', type: 'string', example: 'CHARGING_STATION_MANAGEMENT'),
                                            new OA\Property(property: 'status', type: 'string', example: 'enable'),
                                            new OA\Property(property: 'value', type: 'string'),
                                            new OA\Property(
                                                property: 'config',
                                                type: 'object',
                                                properties: [
                                                    new OA\Property(property: 'partner', type: 'string', example: 'f2mc'),
                                                    new OA\Property(property: 'enrolmentStatus', type: 'string', example: 'complete')
                                                ]
                                            )
                                        ]
                                    )
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'error',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'message', type: 'string', example: 'Vehicle does not exist')
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 422,
                description: 'Validation Error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'error',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                                new OA\Property(
                                    property: 'errors',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error',
                content: new JsonContent(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'error',
                            type: 'object',
                            properties: [
                                new OA\Property(property: 'message', type: 'string')
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function getVehicleFeatures(Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        try {
            $userId = $request->headers->get('userId');
            $vin = $request->headers->get('vin');

            $errors = $this->validator->validate(
                compact('vin', 'userId'),
                new Assert\Collection([
                    'vin' => VinValidator::getConstraints(),
                    'userId' => new Assert\NotBlank,
                ])
            );

            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();
                return $this->json($response['content'], $response['code']);
            }

            $response = $vehicleManager->getVehicleFeatures($userId, $vin);
            
            if ($response instanceof ErrorResponse) {
                $this->logger->error(
                    __METHOD__ . ' An error occurred while fetching vehicle features',
                    [
                        $response->getCode(),
                        $response->getErrors()
                    ]
                );
                
                return $this->json(['error' => ['message' => $response->getMessage()]], Response::HTTP_BAD_REQUEST);
            }

            return $this->json($response->toArray(false)['content'], $response->getCode());
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }



    // START::STRICTLY FOR TESTING FEATURE CODES ONLY
    #[Route('/feature-testing', name: 'feature_test', methods: ['POST'])]
    #[OA\Tag(name: 'FOR INTERNAL TESTING ONLY')]
    #[OA\Post(
        path: '/v2/vehicles/feature-test',
        summary: 'FOR INTERNAL TESTING ONLY - DO NOT USE IN PRODUCTION',
        description: 'This endpoint is strictly for internal testing of feature codes. It uses hardcoded test values and should never be used in production environments or by external clients.',
        tags: ['Features test'],
        deprecated: true,
        responses: [
            new OA\Response(
                response: 200,
                description: 'Test response - FOR INTERNAL USE ONLY',
            ),
            new OA\Response(
                response: 500,
                description: 'Internal Server Error',
                content: new JsonContent(
                    properties: [
                        new OA\Property(property: 'error', properties: [
                            new OA\Property(property: 'message', type: 'string')
                        ])
                    ]
                )
            )
        ]
    )]
    public function testFeatureCodes(Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        // TESTING ONLY: Using hardcoded values for internal testing purposes
        $vin = "VYEATTEN0SPU00024";
        $userId = "001xf5d49f0b4dce874859657ae98122";
        $mileage = "0";
        $brand = "JE";
        $country = "IT";
        $language = "fr";
        $source = "SPACEWEB";

        $vehiculeInputs = (new AddVehicleInputDTO())
            ->setVin($vin)
            ->setUserId($userId)
            ->setMileage($mileage)
            ->setBrand($brand)
            ->setCountry($country)
            ->setLanguage($language)
            ->setSource($source);

        //UPDATED_VEHICLE_CONTRACTS
        $vehicleManager->handleCustomerRightsUpdate('UPDATED_VEHICLE_CONTRACTS', $vin, $userId, time(), $userId);
        $response = $vehicleManager->testFeaureCodes($vehiculeInputs)->toArray(false);
        return $this->json($response['content'], $response['code']);
    }
    // END::STRICTLY FOR TESTING FEATURE CODES ONLY
}
