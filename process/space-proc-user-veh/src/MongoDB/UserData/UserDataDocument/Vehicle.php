<?php

namespace App\MongoDB\UserData\UserDataDocument;

use Symfony\Component\Serializer\Attribute\Groups;

class Vehicle
{
    #[Groups(['xPInsert', 'xFInsert'])]
    public string $id = '';

    #[Groups(['xPInsert', 'xFInsert'])]
    public string $vin;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $shortLabel = null;

    #[Groups(['xFUpdate'])]
    public ?string $nickName = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $modelDescription = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $versionId = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $type = null;

    #[Groups(['default'])]
    public ?string $culture = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $brand = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $country = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $picture = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $sdp = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?int $regTimeStamp = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $enrollmentStatus = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $connectorType = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $make = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $subMake = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $market = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $year = null;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?int $lastUpdate;

    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?string $warrantyStartDate = null;

    #[Groups(['default'])]
    public ?bool $isOrder = null;

    #[Groups(['default'])]
    public ?VehicleOrder $vehicleOrder = null;

    /**
     * Array of feature codes associated with the vehicle
     */
    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?array $featureCode = [];

    /**
     * Timestamp for feature code expiry
     */
    #[Groups(['xPUpdate', 'xFUpdate'])]
    public ?int $featureCodeExpiry = null;

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getVersionId(): ?string
    {
        return $this->versionId;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    /**
     * Get feature codes for the vehicle
     * 
     * @return array|null
     */
    public function getFeatureCodes(): ?array
    {
        return $this->featureCode;
    }

    /**
     * Set feature codes for the vehicle
     * 
     * @param array $featureCodes
     * @return self
     */
    public function setFeatureCodes(array $featureCodes): self
    {
        $this->featureCode = $featureCodes;
        return $this;
    }

    /**
     * Find a specific feature code by code name
     * 
     * @param string $code
     * @return array|null
     */
    public function findFeatureCode(string $code): ?array
    {
        if (empty($this->featureCode)) {
            return null;
        }

        foreach ($this->featureCode as $featureCode) {
            if ($featureCode['code'] === $code) {
                return $featureCode;
            }
        }

        return null;
    }

    /**
     * Update a specific feature code's status
     * 
     * @param string $code
     * @param string $status
     * @return bool
     */
    public function updateFeatureCodeStatus(string $code, string $status): bool
    {
        if (empty($this->featureCode)) {
            return false;
        }

        foreach ($this->featureCode as $key => $featureCode) {
            if ($featureCode['code'] === $code) {
                $this->featureCode[$key]['status'] = $status;
                return true;
            }
        }

        return false;
    }

    /**
     * Update a specific feature code's config
     * 
     * @param string $code
     * @param array $config
     * @return bool
     */
    public function updateFeatureCodeConfig(string $code, array $config): bool
    {
        if (empty($this->featureCode)) {
            return false;
        }

        foreach ($this->featureCode as $key => $featureCode) {
            if ($featureCode['code'] === $code) {
                $this->featureCode[$key]['config'] = array_merge(
                    $featureCode['config'] ?? [],
                    $config
                );
                return true;
            }
        }

        return false;
    }

    /**
     * Get feature code expiry timestamp
     * 
     * @return int|null
     */
    public function getFeatureCodeExpiry(): ?int
    {
        return $this->featureCodeExpiry;
    }

    /**
     * Set feature code expiry timestamp
     * 
     * @param int $timestamp
     * @return self
     */
    public function setFeatureCodeExpiry(int $timestamp): self
    {
        $this->featureCodeExpiry = $timestamp;
        return $this;
    }
}
