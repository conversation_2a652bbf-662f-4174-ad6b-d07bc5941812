<?php

namespace App\Model;

class FeatureCode 
{
    const BRAND_JEEP = 'JE';
    const BRAND_ALFA = 'AR';    const J4U_NAV_PROTOCOL = "ble";
    const BATTERY_ELECTRIC_VEHICLE = 'BEV';
    const J4U_DIGITAL_KEY_TYPE = "NEA";
    const A5U_DIGITAL_KEY = "Brain";
    const LEGACY_XF_DIGITAL_KEY_TYPE = "Atlantis";
    const J4U_TRIPS_PROTOCOL = "network";
    const J4U_TRIPS_LOCATION = ["trip", "manual"];
    const NON_NAW01 = "NON_NAW01";    
    const NON_FDS_KEY = "ADD_VEHICLE";
    const REGION_NAFTA = "NAFTA";
    const REGION_EMEA = "EMEA";

    /**
     * Get all non-FDS feature keys dynamically from FeatureCodeConfig::ADD_VEHICLE
     * 
     * @return array
     */
    public static function getNonFdsFeatureKeys(): array
    {
        $addVehicleConfig = FeatureCodeConfig::getFeatureConfig(self::NON_FDS_KEY);
        return $addVehicleConfig ? array_keys($addVehicleConfig) : [];
    }

    private const CSM_ENROLMENT_STATUSES = [
        'NO_PAYMENT_METHOD'   => 'noPaymentMethod',
        'NO_ACCOUNT_LINKING'  => 'noAccountLinking',
        'COMPLETE'          => 'complete',
    ];
    public static function getEnrolmentStatus(string $key): ?string
    {
        return self::CSM_ENROLMENT_STATUSES[$key] ?? null;
    }

    const CSM_PARTNER_F2MC = "F2MC";
    const CSM_PARTNER_TBD = "TBD";

    private string $code;
    private string $status;
    private ?string $value;
    private array $config;

    public function __toArray(){
        return call_user_func('get_object_vars', $this);
    }

    /**
     * Get the value of code
     */ 
    public function getCode(): ?string
    {
        return $this->code;
    }

    /**
     * Set the value of code
     *
     * @return  self
     */ 
    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    /**
     * Get the value of status
     */ 
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Set the value of status
     *
     * @return  self
     */ 
    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get the value of value
     */ 
    public function getValue(): ?string
    {
        return $this->value;
    }

    /**
     * Set the value of value
     *
     * @return  self
     */ 
    public function setValue(?string $value): self
    {
        $this->value = $value;

        return $this;
    }

    /**
     * Get the value of config
     */ 
    public function getConfig(): ?array
    {
        return $this->config;
    }

    /**
     * Set the value of config
     *
     * @return  self
     */ 
    public function setConfig(?array $config): self
    {
        $this->config = $config;

        return $this;
    }
}