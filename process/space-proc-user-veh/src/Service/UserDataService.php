<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\MongoDB\UserData\UserDataDocument\UserDataDocument;
use App\MongoDB\UserData\UserDataDocument\UserDataDocumentCollection;
use App\MongoDB\UserData\UserDataDocument\Vehicle;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;

class UserDataService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer,
        private NormalizerInterface $normalizer,
        private DenormalizerInterface $denormalizer
    )
    {
    }

    public function saveUserDataDocument(UserDataDocument $userDataDocument): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' for userId : '.$userDataDocument->userId);

        if(!empty($userDataDocument->id)) {
            $result = $this->mongoService->updateOne(
                self::COLLECTION,
                ['userId' => $userDataDocument->userId],
                (array) $userDataDocument,
            );
        } else {
            $result = $this->mongoService->insertOne(
                self::COLLECTION,
                (array) $userDataDocument
            );
        }

        if(Response::HTTP_OK !== $result->getCode()) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while saving user data document', ['userId' => $userDataDocument->userId]);
            return new ErrorResponse($result->getData(), $result->getCode());
        }

        return new SuccessResponse($result->getData(), $result->getCode());
    }

    public function getUserDataDocument(string $userId): ?UserDataDocument
    {
        try{
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' for userId : '.$userId);
            $result = $this->mongoService->find(self::COLLECTION, ['userId' => $userId]);

            if(Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user data document', ['userId' => $userId]);
                return null;
            }

            if(empty($result->getData())) {
                return null;
            }

            $userDataDocumentCollection = $this->serializer->deserialize(
                $result->getData(),
                UserDataDocumentCollection::class,
                'json'
            );

            return $userDataDocumentCollection->shiftDocument();
        } catch (\Exception $e) {

            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' An exception occurred while fetching user data document', [                
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }
   
    /**
     * @return array<array-key, string>
     */
    public function fetchACNTCodes(
        UserDataDocument $userDataDocument, 
        array $toSkipBrands = [] 
    ): array
    {
        $list = [];
        if(empty($userDataDocument->userPsaId)){
            return $list;
        }

        foreach($userDataDocument->userPsaId as $psaId) {

            $brand = trim(strtoupper($psaId->brand));
            if (!empty($toSkipBrands) && in_array($brand, $toSkipBrands)){
                continue;
            }

            $acnt = $psaId->getAcntCode();
            if(empty($acnt)){
                $this->logger->error('Invalid PSA ID format: '.$psaId->cvsId, ['userId' => $userDataDocument->userId]);
                continue;
            }

            $list[$brand] = $acnt;
        }
        return $list;
    }

    public function findUserVehicleByVin(string $userId, string $vin): ?Vehicle
    {
        try {
            $filter = [
                'userId' => $userId,
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => [
                            '$regex' => '^' . preg_quote($vin, '/') . '$',
                            '$options' => 'i'
                        ]
                    ]
                ]
            ];
    
            $projection = [
                'vehicle.$' => 1
            ];
    
            $result = $this->mongoService->findOne(self::COLLECTION, $filter, $projection);

            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user vehicle by vin', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
    
                return null;
            }

            $data = json_decode($result->getData(), true);
            $vehicle = null;
            if(isset($data['document']['vehicle'])) {
                $vehicle = $this->denormalizer->denormalize($data['document']['vehicle'][0], Vehicle::class);
            }

            return $vehicle;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user vehicle by vin', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function addVehicleToUserDocument(string $userId, Vehicle $vehicleData, array $context = []): bool
    {
        try{
            $filter = [
                'userId' => $userId
            ];

            $update = [
                'vehicle' => $this->normalizer->normalize($vehicleData, 'array', $context)
            ];

            $result = $this->mongoService->updatePush(self::COLLECTION, $filter, $update, false);
            if(Response::HTTP_OK !== $result->getCode()) {
                throw new \Exception('Error while adding vehicle '. $vehicleData->vin .' to user document for userId: '.$userId);
            }

            return true;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while adding vehicle to user document', [
                'userId' => $userId,
                'vehicleData' => $vehicleData,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function updateVehicleInUserDocument(string $userId, Vehicle $vehicleData, array $context = []): bool
    {
        try {
            $toUpdate = $this->normalizer->normalize($vehicleData, 'array', $context);
            $update = [];
            foreach ($toUpdate as $field => $value) {
                if ($value !== null && $value !== '') {
                    $update["vehicle.$.$field"] = $value;
                }
            }

            // If no fields to update, return a response early
            if (empty($update)) {
                return false;
            }

            $filter = [
                'userId' => $userId,
                'vehicle.vin' => $vehicleData->vin
            ];
            
            $result = $this->mongoService->updateOne(self::COLLECTION, $filter, $update, false);
    
            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while updating vehicle in user document', [
                    'userId' => $userId,
                    'vehicleData' => $vehicleData,
                    'response' => $result->getData()
                ]);
                throw new \Exception('Error while updating vehicle '. $vehicleData->vin .' in user document for userId: '.$userId);
            }
    
            return true;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while updating vehicle in user document', [
                'userId' => $userId,
                'vehicleData' => $vehicleData,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get a list of vehicles filtered by brand for a given userId.
     * @return Vehicle[]
     */
    public function getVehicleByUserIdAndBrand(string $userId, string $brand): array
    {
        $pipeline = [
            [
                '$match' => [
                    'userId' => $userId
                ]
            ],
            [
                '$project' => [
                    'vehicle' => [
                        '$filter' => [
                            'input' => '$vehicle',
                            'as' => 'vehicle',
                            'cond' => [
                                '$eq' => ['$$vehicle.brand', $brand]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    
        $result = $this->mongoService->aggregate(self::COLLECTION, $pipeline);
    
        if (Response::HTTP_OK !== $result->getCode() || empty($result->getData())) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching vehicles by userId and brand', [
                'userId' => $userId,
                'brand' => $brand,
                'response' => $result->getData()
            ]);
            return [];
        }
    
        $data = json_decode($result->getData(), true);
    
        $vehicles = [];
        if (isset($data['documents'][0]['vehicle'])) {
            foreach ($data['documents'][0]['vehicle'] as $vehicleData) {
                $vehicles[] = $this->serializer->deserialize(json_encode($vehicleData), Vehicle::class, 'json');
            }
        }
    
        return $vehicles;
    }

    function deleteVehiclesFieldForUser(string $userId): int
    {     
        // Update the document by unsetting the 'vehicle' field
        $result = $this->mongoService->deleteFields(
            self::COLLECTION,
            ['userId' => $userId], // Filter by userId
            ['$unset' => ['vehicles' => '']] // Remove the 'vehicles' field
        );        

        if (Response::HTTP_OK !== $result->getCode()) {
            return 0;
        }
        $data = json_decode($result->getData(), true);
        return $data['modifiedCount'];
    }

    function removeUserGSPDVehicles(string $userId, string $brand, array $vinExcluded): bool
    {
        $result = $this->mongoService->removeFields(
            self::COLLECTION,
            ['userId' => $userId], // Filter by userId
            ['vehicle' => [
                '$and' => [
                    ['vin' => ['$nin' => $vinExcluded]],
                    ['brand' => ['$eq' => $brand]],
                    ['sdp' => ['$eq' => RefreshVehicleInterface::SDP_GSDP]]
                ],
            ]]
        );

        if (Response::HTTP_OK !== $result->getCode()) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while removing GSDP vehicles from user document', [
                'userId' => $userId,
                'vinExcluded' => $vinExcluded,
                'response' => $result->getData()
            ]);
            return false;
        }
        
        return true;
    }

    // Retrieves the user ID and vehicle associated with a specific vehicle VIN.
    public function getVehicleAndUserIdByVin(string $userId, string $vin): ?array
    {
        try {
            $filter = [
                'userId' => $userId,
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => [
                            '$regex' => '^' . preg_quote($vin, '/') . '$',
                            '$options' => 'i'
                        ]
                    ]
                ]
            ];

            $projection = [
                'userId' => 1,
                'vehicle.$' => 1,
                'f2mc' => 1,
                'userDbId' => 1
            ];

            $result = $this->mongoService->findOne(self::COLLECTION, $filter, $projection);

            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ .  ' Error while fetching vehicle and user ID by VIN', ['vin' => $vin]);
                return null;
            }

            if (empty($result->getData())) {
                return null;
            }

            $data = json_decode($result->getData(), true);

            if (!isset($data['document']['userId']) || !isset($data['document']['vehicle'][0])) {
                return null;
            }

            $userId = $data['document']['userId'];
            $vehicle = $this->denormalizer->denormalize($data['document']['vehicle'][0], Vehicle::class);
            $f2mc = $data['document']['f2mc'][0] ?? null;
            return ['userId' => $userId, 'vehicle' => $vehicle, 'f2mc' => $f2mc];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ .  ' An exception occurred while fetching vehicle and user ID by VIN', [
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    //Updates the feature code array for a specific user's vehicle by VIN.
    public function updateFeatureCodes(string $userId, string $vin, array $featureCodes)
    {
        try {
            $featureCodeExpiry = time() + (24 * 60 * 60);

            $filter = [
                'userId' => $userId,
                'vehicle.vin' => $vin,
            ];

            $update = [
                'vehicle.$.featureCode' => $featureCodes,
                'vehicle.$.featureCodeExpiry' => $featureCodeExpiry
            ];

            $result = $this->mongoService->updateOne(self::COLLECTION, $filter, $update);

            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__.' updateUserVehiclesFeatureCodesByVin::Error while saving feature codes for vehicle by vin', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'featureCode' => $featureCodes
                ]);
                return false;
            }

            return true;
        } catch (\Throwable $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__.' updateUserVehiclesFeatureCodesByVin::Error while saving feature codes for vehicle by vin', [
                'userId' => $userId,
                'vin' => $vin,
                'featureCode' => $featureCodes,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    // Retrieves the user ID and vehicle associated with a specific vehicle VIN.
    public function getVehicleAndUserDBIdByUserIdAndVin(string $userId, string $vin): ?array
    {
        try {
            $filter = [
                'userId' => $userId,
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => [
                            '$regex' => '^' . preg_quote($vin, '/') . '$',
                            '$options' => 'i'
                        ]
                    ]
                ]
            ];

            $projection = [
                'userDbId' => 1,
                'vehicle.$' => 1
            ];

            $result = $this->mongoService->findOne(self::COLLECTION, $filter, $projection);

            if (Response::HTTP_OK !== $result->getCode()) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ .  ' Error while fetching vehicle and user ID by userId and VIN',
                                         ['userId' => $userId, 'vin' => $vin]);
                return null;
            }

            if (empty($result->getData())) {
                return null;
            }

            $data = json_decode($result->getData(), true);

            if (!isset($data['document']['userDbId']) || !isset($data['document']['vehicle'][0])) {
                return null;
            }

            $userDbId = $data['document']['userDbId'];
            $vehicle = $this->denormalizer->denormalize($data['document']['vehicle'][0], Vehicle::class);

            return ['userDbId' => $userDbId, 'vehicle' => $vehicle];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ .  ' An exception occurred while fetching details by userId & VIN', [
                'userId' => $userId, 'vin' => $vin,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    function removeUserSSDPVehicles(string $userId, string $vin): bool
    {
        $result = $this->mongoService->removeFields(
            self::COLLECTION,
            ['userId' => $userId], // Filter by userId
            ['vehicle' => [
                '$and' => [
                    ['vin' => $vin],
                    ['sdp' => 'SSDP']
                ],
            ]]
        );

        if (Response::HTTP_OK !== $result->getCode()) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while removing SSDP vehicles from user document', [
                'userId' => $userId,
                'vin' => $vin,
                'response' => $result->getData()
            ]);
            return false;
        }
        
        return true;
    }

    function getVehicle(string $userId, string $critariaValue, string $critariaKey = 'vin'): ?array
    {
        $filter = [
            'userId' => $userId,
            'vehicle' => [
                '$elemMatch' => [
                    $critariaKey => $critariaValue                ]
            ]
        ];
    
        $projection = [
            'vehicle.$' => 1,
            'userId' => 1,
            'userDbId' => 1,
        ];
        
        $vehicle = $this->findInMongoBy(
            'userData',
            $filter,
            $projection
        );


        return $vehicle ?? null;
    }

    private function findInMongoBy(
        string $collection,
        array $filters,
        ?array $projection = []
    ): ?array {
        $response = $this->mongoService->find(
            $collection,
            $filters,
            $projection
        );
 
        return json_decode($response->getData(), true)['documents'][0] ?? [];
    }
}