<?php

namespace App\Manager;

use App\Exception\BadRequestException;
use App\Exception\TokenNotFoundException;
use App\Helper\ErrorResponse;
use App\Helper\IResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Service\ChargingSessionService;
use App\Service\UserService;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UserManager
{
    use LoggerTrait;
    public const CPT = 'CPT';

    public function __construct(
        private UserService $userService,
        private ValidatorInterface $validator,
        private ChargingSessionService $chargingSessionService,
        private string $accountLinkingRedirectUrl
    ) {}

    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId)
    {
        $this->logger->info('Getting users', ['userId' => $userId, 'vin' => $vin, 'sessionId' => $sessionId]);
        $users = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);

        return $users;
    }

    public function getUserByUserId($userId)
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        $user = $this->userService->getUserByUserId($userId);

        return $user;
    }

    public function register(array $params, string $userId): IResponseArrayFormat
    {
        $this->logger->info('=> Calling F2MC SYSTEM LAYER: ' . __METHOD__, ['params' => $params]);
        try {
            $vin = $params['vin'] ?? null;
            if ($vin) {
                $dataInfo = json_decode($this->userService->getUserByUserIdAndVin($userId, $vin)->getData(), true)['documents'][0] ?? [];
                if (empty($dataInfo)) {
                    return (new ErrorResponse())->setMessage('User with associated vin not found')->setCode(Response::HTTP_NOT_FOUND);
                }
            } else {
                $dataInfo = json_decode($this->userService->getUserByUserId($userId)->getData(), true)['documents'][0] ?? [];
                if (empty($dataInfo)) {
                    return (new ErrorResponse())->setMessage('User not found')->setCode(Response::HTTP_NOT_FOUND);
                }
            }
            $profileData = $dataInfo['profile'] ?? [];
            $this->addProfileInfo($params, $profileData);
            $constraints = new Assert\Collection([
                'firstName' => [new Assert\NotBlank(), new Assert\Length(['max' => 50])],
                'lastName' => [new Assert\NotBlank(), new Assert\Length(['max' => 50])],
                'email' => [new Assert\NotBlank(), new Assert\Email()],
                'zipCode' => [new Assert\NotBlank(), new Assert\Length(['min' => 5, 'max' => 10]), new Assert\Type('digit')],
                'country' => new Assert\NotBlank(),
                'countryCode' => [new Assert\NotBlank(), new Assert\Length(['min' => 2, 'max' => 2])],
                'state' => new Assert\NotBlank(),
                'city' => new Assert\NotBlank(),
                'vin' => new Assert\Optional([new Assert\Length(['min' => 17, 'max' => 17])])
            ]);

            $errors = $this->validator->validate($params, $constraints);
            if ($errors->count()) {
                return (new ErrorResponse())
                    ->setMessage(null)
                    ->setItems('enrolmentStatus', ChargingStationManagementManager::MISSING_PROFILE_DATA)
                    ->setCode(Response::HTTP_FORBIDDEN);
            }
            $response = $this->userService->register(['json' => $params]);
            $responseContent = $response->getData();
            $isVinAdded = $responseContent['success']['profile']['isVinAdded'] ?? false;

            if (Response::HTTP_OK !== $response->getCode()) {
                return $this->handleErrorResponse($responseContent);
            }

            if (!isset($responseContent['success']['accessToken'])) {
                throw TokenNotFoundException::make();
            }

            if ($isVinAdded) {
                $vehicleId = $responseContent['success']['vehicle']['id'] ?? 0;

                $this->userService->updateUserVehicleId($userId, $vehicleId, $vin);
            }
            $this->logger->info('=> ' . __METHOD__ . ' => Success response: ' . json_encode($responseContent));
            $data = [
                'f2mc.accessToken' => $responseContent['success']['accessToken'],
                'f2mc.refreshToken' => $responseContent['success']['refreshToken'],
            ];

            if (isset($responseContent['success']['profile']['id'])) {
                $data['f2mc.userId'] = $responseContent['success']['profile']['id'];
            }

            if (isset($responseContent['success']['profile']['isNewUser'])) {
                $data['f2mc.isPaymentMethod'] = false;
                $data['f2mc.isAccountLinked'] = false;
            }

            $this->userService->registerF2mcUserInMongoDb($data, $userId);

            if ($vin) {
                $updatedUserResponse = $this->userService->getUserByUserIdAndVin($userId, $vin);
            } else {
                $updatedUserResponse = $this->userService->getUserByUserId($userId);
            }
 
            $updatedDataInfo = json_decode($updatedUserResponse->getData(), true)['documents'][0] ?? [];
 
            $f2mcData = $updatedDataInfo['f2mc'] ?? [];
            $isPaymentMethod = $f2mcData['isPaymentMethod'] ?? false;
            $isAccountLinked = $f2mcData['isAccountLinked'] ?? false;
            $accessToken = $data['f2mc.accessToken'] ?? '';
            $enrolmentStatus = '';
            $statusData = [];
            if ($isAccountLinked == true and $isPaymentMethod == true) {
                $enrolmentStatus = ChargingStationManagementManager::COMPLETE;
            } elseif ($isAccountLinked == true and $isPaymentMethod == false) {
                $enrolmentStatus = ChargingStationManagementManager::NO_PAYEMENT_METHOD;
                $response = $this->chargingSessionService->getPaymentMethodUrl($accessToken);
                if ($response->getCode() == Response::HTTP_OK) {
                    $responseData = $response->getData();
                    $responseData = $responseData['data'] ?? $responseData;
                    $statusData['paymentMethodUrl'] = $responseData['url'] ?? null;
                }
            } elseif ($isAccountLinked == false and $isPaymentMethod == true) {
                $enrolmentStatus = ChargingStationManagementManager::NO_ACCOUNT_LINKING;
                $response = $this->userService->getAccountLinkUrl($accessToken, self::CPT);
                if ($response->getCode() == Response::HTTP_OK) {
                    $statusData['accountLinkingUrl'] = $response->getData()['success']['url'] ?? '';
                    $statusData['accountLinkingRedirectUrl'] = $this->accountLinkingRedirectUrl ?? '';
                }
            } else {
                $enrolmentStatus = ChargingStationManagementManager::NO_ACCOUNT_LINKING;
                $response = $this->chargingSessionService->getPaymentMethodUrl($accessToken);
                if ($response->getCode() == Response::HTTP_OK) {
                    $responseData = $response->getData();
                    $responseData = $responseData['data'] ?? $responseData;
                    $statusData['paymentMethodUrl'] = $responseData['url'] ?? null;
                }
                $response = $this->userService->getAccountLinkUrl($accessToken, self::CPT);
                if ($response->getCode() == Response::HTTP_OK) {
                    $statusData['accountLinkingUrl'] = $response->getData()['success']['url'] ?? '';
                    $statusData['accountLinkingRedirectUrl'] = $this->accountLinkingRedirectUrl ?? '';
                }
            }
            if ($vin) {
                $this->userService->updateUserVehiclesFeatureCodeByStatusAndVin($userId, $enrolmentStatus, $vin);
            } else {
                $this->userService->updateUserVehiclesFeatureCodeByStatus($userId, $enrolmentStatus);
            }
            $statusData['enrolmentStatus'] = $enrolmentStatus;
            return (new SuccessResponse())->setData($statusData);
        } catch (\Exception $e) {
            $this->logger->error('=> Exception in ' . __METHOD__ . ': ' . $e->getMessage());
            throw BadRequestException::make();
        }
    }

    public function getAccountLinkUrl(string $userId, string $providerShortCode): IResponseArrayFormat
    {
        try {
            if (!$token = $this->getTokenByUserId($userId)) {
                return (new ErrorResponse())->setMessage('Token not found')->setCode(Response::HTTP_BAD_REQUEST);
            }
            $this->logger->info(__CLASS__ . "::" . __METHOD__ . ": start calling sys-f2mc getting account link url");
            $response = $this->userService->getAccountLinkUrl($token, $providerShortCode);
            if ($response->getCode() == Response::HTTP_OK) {
                $result['accountLinkingUrl'] = $response->getData()['success']['url'] ?? '';
                $result['accountLinkingRedirectUrl'] = $this->accountLinkingRedirectUrl ?? '';

                return (new SuccessResponse())->setData($result)->setCode($response->getCode());
            }
            $result = $response->getData()['error']['message'] ?? 'Error while getting account link url';

            return (new ErrorResponse())->setMessage($result)->setCode($response->getCode() ?: Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . "::" . __METHOD__ . ": Catched Exception while  getting account link url " . $e->getMessage());

            return (new ErrorResponse())->setMessage($e->getMessage())->setCode($e->getCode());
        }
    }

    private function handleErrorResponse(array $responseContent): void
    {
        $errorMessage = $responseContent['error']['message'] ?? json_encode($responseContent);
        $this->logger->error('=> ' . __METHOD__ . ' => Error: ' . $errorMessage);
        throw BadRequestException::make();
    }

    private function buildRegisterResponse($url): array
    {
        return [
            'status' => 'pending_payment_method',
            'featureCode' => 'Charging_station',
            'featureCodeValue' => 'pending_payment_method',
            'paymentMethodUrl' => $url,
        ];
    }

    private function getTokenByUserId(string $userId): ?string
    {
        $user = $this->userService->getUserByUserId($userId);
        $data = json_decode($user->getData(), true);

        return $data['documents'][0]['f2mc']['accessToken'] ?? null;
    }

    private function addProfileInfo(array &$params, array $data)
    {
        $params['email'] = $data['email'] ?? "";
        $params['firstName'] = $data['firstName'] ?? "";
        $params['lastName'] = $data['lastName'] ?? "";
        $params['zipCode'] = $data['zipcode'] ?? "";
        $params['country'] = $data['country'] ?? "";
        $params['countryCode'] = $data['countryCode'] ?? "US";
        $params['state'] = $data['state'] ?? "Massachusetts";
        $params['city'] = $data['city'] ?? "";
    }

    private function getInfoByKey(?array $data = [], string $key = "", $field = "code")
    {
        return current(array_filter($data, function ($item) use ($key, $field) {
            return $item[$field] == $key;
        }));
    }
}
