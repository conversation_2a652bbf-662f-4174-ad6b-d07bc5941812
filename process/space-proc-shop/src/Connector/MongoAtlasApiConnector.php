<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * The connector with the AtlasMongoDB API.
 */
class MongoAtlasApiConnector
{
    use LoggerTrait;

    public function __construct(
        private HttpClientInterface $mongoAtlasClient,
        private string $mongoApp
    ) {
        $this->mongoApp = $mongoApp;
    }

    /**
     * Call Mongo Atlas api.
     *
     * @param mixed|array $options
     */
    public function call(string $method, string $url, mixed $options = []): WSResponse
    {
        try {
            $response = $this->mongoAtlasClient->request($method, $url, (array) $options);
            $this->logger->info('MongoAtlasApiConnector::call '.$url.' options '.json_encode($options));

            return new WSResponse($response->getStatusCode(), $response->getContent(false));
        } catch (\Exception $e) {
            $this->logger->error('error occured while calling '.$url.'error: '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * build mongoAtlas endpoint.
     */
    public function getEndpoint(string $action): string
    {
        return sprintf('/app/%s/endpoint/data/v1/action/%s', $this->mongoApp, $action);
    }
}
