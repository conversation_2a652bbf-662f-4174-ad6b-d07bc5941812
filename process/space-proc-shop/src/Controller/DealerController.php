<?php

namespace App\Controller;

use App\Enum\DealerFilterEnum;
use App\Helper\BrandHelper;
use App\Helper\MarketHelper;
use App\Helper\RegionHelper;
use App\Manager\DealerManager;
use App\Model\DealerV2Model;
use App\Trait\ValidationResponseTrait;
use App\Transformer\xfEmeaParameterRequestTransformer;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/v1', name: 'dealer_')]
class DealerController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/dealers-list', name: 'list', methods: ['GET'])]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'latitude',
        in: 'query',
        description: 'latitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'longitude',
        in: 'query',
        description: 'longitude',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'rmax',
        in: 'query',
        description: 'rmax',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'resultmax',
        in: 'query',
        description: 'resultmax',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'criterias',
        in: 'query',
        description: 'Criterias, example : VN, APV, VO',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'model',
        in: 'query',
        description: 'model o2x',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'source',
        required: false,
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB']
        )
    )]
    #[OA\Parameter(
        name: 'country_apv',
        in: 'query',
        description: 'country_apv',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: DealerV2Model::class)
    )]
    #[OA\Response(
        response: 422,
        description: 'Validation error',
        content: new OA\JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', type: 'object')
                ]),
            ]
        )
    )]
    #[OA\Tag(name: 'Dealer API')]
    public function list(ValidatorInterface $validator, DealerManager $manager, Request $request): JsonResponse
    {

        $brand = $request->query->get('brand', '');
        $country = $request->query->get('country', '');
        $language = $request->query->get('language', '');
        $latitude = $request->query->get('latitude', '');
        $longitude = $request->query->get('longitude', '');
        $rmax = $request->query->get('rmax', DealerFilterEnum::FILTER_RMAX);
        $resultMax = $request->query->get('resultmax', DealerFilterEnum::FILTER_RESULTMAX);
        $criterias = $request->query->get('criterias', DealerFilterEnum::FILTER_CRITERIAS);
        $model = $request->query->get('model', '');
        $source = $request->query->get('source', 'APP');
        $country_apv = $request->query->get('country_apv', '');
        $context = [
            AbstractObjectNormalizer::PRESERVE_EMPTY_OBJECTS => true,
            AbstractObjectNormalizer::SKIP_NULL_VALUES => TRUE,
        ];
        if ($country_apv == '') {
            $errors = $validator->validate(
                compact('brand', 'country', 'language', 'latitude', 'longitude'),
                new Assert\Collection([
                    'brand' => BrandValidator::getConstraints(),
                    'country' => CountryValidator::getConstraints(),
                    'language' => LanguageValidator::getConstraints(),
                    'latitude' => new Assert\NotBlank(),
                    'longitude' => new Assert\NotBlank()
                ])
            );
        } else {
            $errors = $validator->validate(
                compact('brand', 'country', 'language', 'latitude', 'longitude', 'country_apv'),
                new Assert\Collection([
                    'brand' => BrandValidator::getConstraints(),
                    'country' => CountryValidator::getConstraints(),
                    'language' => LanguageValidator::getConstraints(),
                    'latitude' => new Assert\NotBlank(),
                    'longitude' => new Assert\NotBlank(),
                    'country_apv' => CountryValidator::getConstraints(),
                ])
            );
        }
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($response['content'], $response['code']);
        }
        if ($country_apv == '') {
            $marketCode = MarketHelper::getMarket($country);
            $marketRegion = MarketHelper::getRegion($country);
        } else {
            $marketCode = MarketHelper::getMarket($country_apv);
            $marketRegion = MarketHelper::getRegion($country_apv);
        }
        if (BrandHelper::isXf($brand) && $marketRegion == RegionHelper::EMEA) {
            $brandCode = BrandHelper::getCode($brand);
            $xfEmeaParameterRequest = xfEmeaParameterRequestTransformer::mapper(
                $request->query->all(),
                $marketCode,
                $brandCode
            );
            $errors = $validator->validate($xfEmeaParameterRequest);

            $messages = $this->getValidationMessages($errors);
            if (!empty($messages)) {
                $response = $this->getValidationErrorResponse($messages)->toArray();

                return $this->json($response['content'], $response['code']);
            }

            $response = $manager->getDealerXfEmeaList($xfEmeaParameterRequest)->toArray(false);
        } else if (!BrandHelper::isXf($brand)) {
            $params = [
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'criterias' => $criterias,
                'rmax' => $rmax,
                'resultmax' => $resultMax,
                'model' => $model,
                'source' => $source,
                'countryApv' => $country_apv
            ];

            $response = $manager->getDealerList($params)->toArray(false);
        } else {
            return $this->json([
                "error" => [
                    "message" => "Dealer Not Found"
                ]
            ], 404);
        }

        return $this->json(
            $response['content'],
            $response['code'],
            [],
            $context
        );
    }
}
