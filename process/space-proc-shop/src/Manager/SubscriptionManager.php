<?php

namespace App\Manager;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Webmozart\Assert\Assert as ArrayAssert;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

use App\Model\SamsContractSuccessResponse;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use App\Model\SubscriptionModel;
use App\Model\SamsRatePlanModel;
use App\Service\ContribService;
use App\Service\SubscriptionService;
use App\Trait\ValidationResponseTrait;
use App\Helper\BrandHelper;
use App\DataMapper\SubscriptionDataMapper;

class SubscriptionManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    const SOURCE_APP = 'APP';

    public function __construct(
        private SubscriptionService $subscriptionService,
        private ContribService $contribService,
        private ValidatorInterface $validator,
        private SubscriptionDataMapper $subscriptionDataMapper,
        private UserManager $userManager
    ) {
    }

    public function subscriptionModelObjectCreation($userId, $apiData)
    {
        $vehicleProvisionning = $apiData;
        $subscription = $vehicleProvisionning['subscription'];
        $brand = $vehicleProvisionning['brand'];
        $cultureCode = $subscription['cultureCode'];
        $productId = $subscription['ratePlans'][0]['product']['productCode'];
        $errors = $this->validator->validate(compact('brand', 'cultureCode', 'productId'), [
            new Assert\Collection([
                'brand' => new Assert\NotBlank(),
                'cultureCode' => new Assert\NotBlank(),
                'productId' => new Assert\NotBlank(),
            ])
        ]);
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return new ErrorResponse("No proper subscription data", 500);
        }
        $contribData = $this->contribService->getContrib($brand, $cultureCode, $productId, self::SOURCE_APP);
        if ($contribData->getCode() !== Response::HTTP_OK) {
            $responseData = $contribData->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
            $this->logger->error('=> '.__METHOD__.' => error : '.$result);
            return new ErrorResponse($result, 500);
        }
        $contribData = $contribData->getData()['success'];
        $subscription = $this->subscriptionDataMapper->createList($subscription, $vehicleProvisionning, $contribData);
        return $subscription;
    }

    public function getSubscription(string $userId, string $vin, string $target): ResponseArrayFormat
    {  
        $this->logger->info('=> Call Subscription API : '.__METHOD__.' with parameters : ', ['userId' => $userId, 'vin' => $vin, 'target' => $target]);
        try {
            $user = $this->userManager->getUserByUserId($userId);
            if ($user->getCode() !== Response::HTTP_OK) {
                return new ErrorResponse($user->getData(), $user->getCode());
            }
            $userData = (array) json_decode($user->getData(), true)['documents'] ?? [];

            if (empty($userData)) {
                $this->logger->error('No user found', ['userId' => $userId]);
                throw new UnrecoverableMessageHandlingException('No user found');
            }
            $response = $this->subscriptionService->getSubscription($userId, $vin, $target);
            if ($response->getCode() !== Response::HTTP_OK) {
                $responseData = $response->getData();
                $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                $this->logger->error('=> '.__METHOD__.' => error : '.$result);
                return new ErrorResponse($result, $response->getCode());
            }
            else {
                $this->logger->info("Success: [function getSubscription] Call SAMS contract for vin {$vin}");
                $subscriptions = [];
                $contracts = $response->getData()['success'];
                ArrayAssert::isArray($contracts);
                ArrayAssert::keyExists($contracts, 'vehicleProvisionings', 'vehicleProvisionings key not found in the sams responses');
                $contracts = $contracts['vehicleProvisionings'];
                foreach ($contracts as $contract) {
                    $subscription = $this->subscriptionModelObjectCreation($userId, $contract);
                    if (is_array($subscription)) {
                        $subscriptions[] = $subscription;
                    }
                    else {
                        return $subscription;
                    }
                }
                return new SuccessResponse($subscriptions);
            }
        } catch (\Exception $e) {
            $this->logger->error('=> '.__METHOD__.'Catched Exception SubscriptionManager::getSubscription '.$e->getMessage());
            return new ErrorResponse($e->getMessage());
        }
    }
}
