<?php

namespace App\Manager;

use App\Trait\LoggerTrait;
use App\Service\UserService;

class UserManager
{
    use LoggerTrait;
    public function __construct(
        private UserService $userService
    ) {
    }

    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId)
    {
        $this->logger->info('Getting users', ['userId' => $userId, 'vin' => $vin, 'sessionId' => $sessionId]);
        $users = $this->userService->getUsersBySessionId($userId, $vin, $sessionId);
        return $users;
    }   

    public function getUserByUserId($userId)
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        $user = $this->userService->getUserByUserId($userId);
        return $user;
    }

    public function updateVehicleFeatureCode($userId, $vin, $featureCode, $featureStatus)
    {
        $this->logger->info('Updating user vehicle for feature code and status', ['userId' => $userId, 'vin' => $vin]);
        $user = $this->userService->updateVehicleFeatureCode($userId, $vin, $featureCode, $featureStatus);
        return $user;
    }
}