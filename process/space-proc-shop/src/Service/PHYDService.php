<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use App\Model\VehicleModel;
use Symfony\Component\Serializer\SerializerInterface;


/**
 * PHYD service.
 */
class PHYDService
{
    use LoggerTrait;

    private VehicleModel $vehicleModel;

    public const COLLECTION = 'drivingScore';

    public const FEATURE_CODE_PHYD = 'UBI_PHYD';

    public const FEATURE_CODE_STATUS_ENABLE = 'enable';
    public const FEATURE_CODE_STATUS_DISABLE = 'disable';

    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * PHYD - Getting Driving Score Data
     *
     * @param string $vin The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to include in the filter.
     */
    public function getDrivingScore(string $vin, ?string $stliPolicyNumber = null)
    {
        $this->logger->info('Getting driving score', context: [
            'vin' => $vin,
            'stliPolicyNumber' => $stliPolicyNumber,
        ]);

        return $this->mongoService->aggregate(self::COLLECTION, isset($stliPolicyNumber) ? $this->getDrivingScoreFilter($vin, $stliPolicyNumber) : $this->getDrivingScoreFilter($vin));
    }

    /**
     * Constructs a MongoDB filter for fetching driving score data based on VIN
     * and optionally stliPolicyNumber.
     *
     * @param string|null $vin              The Vehicle Identification Number (VIN) to filter by.
     * @param string|null $stliPolicyNumber (Optional) The STLI Policy Number to filter by.
     */
    public function getDrivingScoreFilter(?string $vin, ?string $stliPolicyNumber = null)
    {
        $filter = [
            [
                '$match' => ['vin' => $vin],
            ],
        ];

        if (!empty($stliPolicyNumber)) {
            $filter[0]['$match']['stliPolicyNumber'] = $stliPolicyNumber;
        }
        return $filter;
    }
}
