<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use App\Helper\WSResponse;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

class UserService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';

    public function __construct(
        private MongoAtlasQueryService $mongoService
    ) {}

    /**
     * Generates a MongoDB filter to find users based on user ID, VIN, and session ID.
     *
     * @param string|null $userId    The ID of the user.
     * @param string|null $vin       The Vehicle Identification Number.
     * @param string|null $sessionId The remote session ID.
     *
     * @return array A MongoDB aggregation pipeline to find matching users and their session details.
     */
    public function getUsersBySessionIdFilter(?string $userId, ?string $vin, ?string $sessionId)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId, 'vehicle.vin' => $vin]
                ],
                ['$unwind' => '$push'],
                [
                    '$match' => [
                        'push.commandSession.remoteSessionId' => $sessionId,
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'pushDetails',
                        'push' => [
                            '$push' => '$push',
                        ],
                    ],
                ],
            ];
    }

    /**
     * Generates a MongoDB filter to find users based on their user ID.
     *
     * @param string|null $userId The ID of the user to search for.
     *
     * @return array A MongoDB aggregation pipeline to find a matching user by user ID.
     */
    public function getUserByUserIdFilter(?string $userId)
    {
        return
            [
                [
                    '$match' => ['userId' => $userId],
                ],
            ];
    }

    /**
     * Retrieves user data from MongoDB based on user ID, VIN, and session ID.
     *
     * @param string|null $userId    The ID of the user.
     * @param string|null $vin       The Vehicle Identification Number.
     * @param string|null $sessionId The remote session ID.
     *
     * @return WSResponse A response containing the user data that matches the specified filters.
     */
    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId): WSResponse
    {
        $this->logger->info('Getting users', ['sessionId' => $sessionId]);
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUsersBySessionIdFilter($userId, $vin, $sessionId));
    }

    /**
     * Retrieves user data from MongoDB based on the user ID.
     *
     * @param string|null $userId The ID of the user to search for.
     *
     * @return WSResponse A response containing the user data that matches the specified user ID.
     */
    public function getUserByUserId(?string $userId): WSResponse
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        return $this->mongoService->aggregate(self::COLLECTION, $this->getUserByUserIdFilter($userId));
    }

    /**
     * Updates the feature code and status for a specific vehicle of a user in the MongoDB collection.
     *
     * @param string $userId       The ID of the user.
     * @param string $vin          The Vehicle Identification Number (VIN) of the vehicle to update.
     * @param string $featureCode  The feature code to set for the vehicle.
     * @param string $featureStatus The status of the feature to set for the vehicle.
     */
    public function updateVehicleFeatureCode($userId, $vin, $featureCode, $featureStatus)
    {
        try {
            $filter = [
                'userId' => $userId,
                'vehicle' => [
                    '$elemMatch' => [
                        'vin' => $vin,
                        'featureCode' => [
                            '$elemMatch' => [
                                'code' => $featureCode,
                            ],
                        ],
                    ],
                ],
            ];
            $updatedFeatureCodes = [];
            $response = $this->mongoService->find(self::COLLECTION, $filter);

            if (Response::HTTP_OK === $response->getCode()) {
                $response = json_decode($response->getData(), true);
                Assert::isArray($response);
                Assert::keyExists($response, 'documents');
                if (count($response['documents']) > 0) {
                    $userDocument = $response['documents'][0];

                    foreach ($userDocument['vehicle'] as $vehicle) {
                        if ($vehicle['vin'] === $vin && isset($vehicle['featureCode'])) {
                                foreach ($vehicle['featureCode'] as &$featureCodes) {
                                    if ($featureCodes['code'] === $featureCode) {
                                        $featureCodes['status'] = $featureStatus;
                                    }
                                $updatedFeatureCodes = $vehicle['featureCode'];
                            }
                        }
                    }
                } 
            }
            
            $update = [
                'vehicle.$.featureCode' => $updatedFeatureCodes
            ];

            return $this->mongoService->updateOne(self::COLLECTION, $filter, $update);
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error updating vedicle featureCode', __METHOD__),
                [
                    'user_id' => $userId,
                    'vin' => $vin,
                    'featureCode' => $featureCode,
                    'featureStatus' => $featureStatus,
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }   
}
